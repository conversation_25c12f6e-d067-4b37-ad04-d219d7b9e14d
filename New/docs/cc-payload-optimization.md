# CC Patient Update Payload Optimization

## Overview

This document describes the optimization made to the CliniCore (CC) patient update API request payload by simplifying the customFields structure.

## Problem

Previously, CC patient update requests were sending complete field objects with extensive metadata that the API doesn't require for updates, resulting in unnecessarily large payloads.

## Solution

Modified the customFields payload structure to only include the field ID in the field object, removing all unnecessary metadata.

## Payload Structure Comparison

### Before Optimization

```json
{
  "customFields": [
    {
      "field": {
        "id": 52,
        "name": "phonemobile",
        "type": "text",
        "label": "phoneMobile",
        "positions": [
          {
            "id": 1,
            "order": 1,
            "tab": 1,
            "customField": 52
          }
        ],
        "isRequired": false,
        "validation": "{}",
        "allowMultipleValues": false,
        "useCustomSort": null,
        "color": null,
        "allowedValues": [],
        "defaultValues": []
      },
      "values": [
        {
          "value": "+8801650483486"
        }
      ]
    }
  ]
}
```

### After Optimization

```json
{
  "customFields": [
    {
      "field": {
        "id": "52"
      },
      "values": [
        {
          "value": "+8801650483486"
        }
      ]
    }
  ]
}
```

## Implementation Details

### Changes Made

1. **Type Definition**: Exported the existing `CCFieldPayload` interface from `New/src/type/CCTypes.ts`
2. **Function Update**: Modified `updateCcPatientCustomFields` in `New/src/processors/patientCustomFields/patientDataFetcher.ts` to use the simplified payload structure
3. **Payload Construction**: Changed from sending complete `GetCCCustomField` objects to only sending `{ id: string }`

### Code Changes

```typescript
// Before
const customFieldsPayload: PostCCPatientCustomfield[] = customFields.map(
  (cf) => ({
    field: cf.field,  // Complete field object
    values: cf.values,
  }),
);

// After
const customFieldsPayload: CCFieldPayload[] = customFields.map(
  (cf) => ({
    field: { id: cf.field.id.toString() },  // Only field ID
    values: cf.values,
  }),
);
```

## Benefits

1. **Reduced Payload Size**: Significantly smaller request payloads
2. **Improved Performance**: Faster API calls due to reduced data transfer
3. **Cleaner Code**: More focused and maintainable payload structure
4. **API Efficiency**: Only sends required data to the CC API

## Compatibility

- ✅ Maintains full functionality with the CC API
- ✅ No breaking changes to existing code
- ✅ Uses existing type definitions (`CCFieldPayload`)
- ✅ Preserves the `values` array structure unchanged

## Files Modified

- `New/src/type/CCTypes.ts` - Exported `CCFieldPayload` interface
- `New/src/processors/patientCustomFields/patientDataFetcher.ts` - Updated payload construction

## Testing

The optimization maintains the same API behavior while reducing payload size. The CC API accepts both the simplified and complete field structures, ensuring backward compatibility.
